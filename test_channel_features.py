#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار ميزات معرف القناة الجديدة
"""

import sys
import os

# إعداد الترميز للعربية
sys.stdout.reconfigure(encoding='utf-8')
sys.stderr.reconfigure(encoding='utf-8')

# إضافة مجلد المشروع للمسار
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from telegram_bot_controller import TelegramBotController

def test_channel_identifier_detection():
    """اختبار اكتشاف معرفات القنوات"""
    print("🧪 اختبار اكتشاف معرفات القنوات")
    print("=" * 50)
    
    bot = TelegramBotController()
    
    test_cases = [
        "@duhffduuh",
        "https://t.me/duhffduuh",
        "t.me/duhffduuh",
        "telegram.me/duhffduuh",
        "-1001234567890",
        "1234567890",
        "not_a_channel",
        "@a",
        "123"
    ]
    
    print("📋 نتائج الاختبار:")
    for test_case in test_cases:
        is_channel = bot._is_channel_identifier(test_case)
        extracted = bot._extract_channel_username(test_case)
        
        status = "✅ قناة" if is_channel else "❌ ليس قناة"
        print(f"  {test_case:<25} -> {status}")
        if is_channel and extracted != test_case:
            print(f"    🔄 المعرف المعالج: {extracted}")

def test_channel_extraction():
    """اختبار استخراج أسماء المستخدمين من الروابط"""
    print("\n🔗 اختبار استخراج أسماء المستخدمين")
    print("=" * 50)
    
    bot = TelegramBotController()
    
    test_cases = [
        "https://t.me/duhffduuh",
        "https://t.me/duhffduuh?start=123",
        "t.me/duhffduuh",
        "telegram.me/duhffduuh",
        "@duhffduuh",
        "-1001234567890"
    ]
    
    print("📋 نتائج الاستخراج:")
    for test_case in test_cases:
        extracted = bot._extract_channel_username(test_case)
        print(f"  {test_case:<30} -> {extracted}")

def main():
    """الدالة الرئيسية"""
    print("🕌 اختبار ميزات معرف القناة الجديدة")
    print("=" * 60)
    
    try:
        test_channel_identifier_detection()
        test_channel_extraction()
        
        print("\n✅ انتهى الاختبار بنجاح!")
        print("\n💡 الميزات الجديدة:")
        print("  • اكتشاف تلقائي لمعرفات القنوات في الرسائل")
        print("  • استخراج أسماء المستخدمين من الروابط")
        print("  • الحصول التلقائي على المعرفات الرقمية")
        print("  • تحديث المعرفات النصية إلى رقمية")
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
