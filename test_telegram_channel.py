#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار معالجة رابط قناة Telegram
"""

import os
from dotenv import load_dotenv

# تحميل متغيرات البيئة
load_dotenv()

def test_channel_username_extraction():
    """اختبار استخراج اسم المستخدم من الرابط"""
    print("🧪 اختبار معالجة رابط القناة")
    print("=" * 40)
    
    # محاكاة الدالة
    def extract_channel_username(channel_input: str) -> str:
        """استخراج اسم المستخدم من رابط القناة أو المعرف"""
        if not channel_input:
            return channel_input
        
        # إزالة المسافات
        channel_input = channel_input.strip()
        
        # إذا كان رابط كامل
        if channel_input.startswith('https://t.me/'):
            username = channel_input.replace('https://t.me/', '')
            return f"@{username}" if not username.startswith('@') else username
        
        # إذا كان يبدأ بـ t.me/
        elif channel_input.startswith('t.me/'):
            username = channel_input.replace('t.me/', '')
            return f"@{username}" if not username.startswith('@') else username
        
        # إذا كان اسم مستخدم فقط بدون @
        elif not channel_input.startswith('@') and not channel_input.startswith('-'):
            return f"@{channel_input}"
        
        # إذا كان معرف صحيح بالفعل
        return channel_input
    
    # اختبار حالات مختلفة
    test_cases = [
        "https://t.me/duhffduuh",
        "t.me/duhffduuh",
        "duhffduuh",
        "@duhffduuh",
        "-1001234567890"  # معرف رقمي
    ]
    
    print("📋 نتائج الاختبار:")
    for test_input in test_cases:
        result = extract_channel_username(test_input)
        print(f"  📥 المدخل: {test_input}")
        print(f"  📤 النتيجة: {result}")
        print()
    
    # اختبار القيمة الحالية من .env
    current_channel = os.getenv('TELEGRAM_CHANNEL_ID', '')
    if current_channel:
        result = extract_channel_username(current_channel)
        print(f"🔧 القيمة الحالية في .env:")
        print(f"  📥 المدخل: {current_channel}")
        print(f"  📤 النتيجة: {result}")
    
    return True

def test_telegram_publisher():
    """اختبار ناشر Telegram مع الرابط الجديد"""
    print("\n📱 اختبار ناشر Telegram")
    print("=" * 40)
    
    try:
        from modules.telegram_publisher import TelegramPublisher
        
        # التحقق من وجود رمز البوت
        bot_token = os.getenv('TELEGRAM_BOT_TOKEN')
        if not bot_token:
            print("⚠️ رمز Telegram Bot غير متوفر")
            print("💡 أضف TELEGRAM_BOT_TOKEN في ملف .env")
            return False
        
        # إنشاء ناشر Telegram
        publisher = TelegramPublisher()
        print(f"✅ تم إنشاء ناشر Telegram")
        print(f"🎯 القناة المستهدفة: {publisher.channel_id}")
        
        # اختبار الاتصال (بدون إرسال رسالة فعلية)
        print("🔍 اختبار إعدادات البوت...")
        
        # يمكن إضافة اختبار اتصال هنا إذا أردت
        # لكن سنتجنب إرسال رسائل تجريبية
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار ناشر Telegram: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🕌 اختبار إعدادات Telegram للقناة")
    print("=" * 50)
    
    # اختبار معالجة الرابط
    test_channel_username_extraction()
    
    # اختبار ناشر Telegram
    test_telegram_publisher()
    
    print("\n" + "=" * 50)
    print("✅ انتهى الاختبار!")
    print("💡 إذا كانت النتائج صحيحة، يمكنك تشغيل التطبيق الرئيسي")
    print("🚀 python app.py")

if __name__ == "__main__":
    main()
