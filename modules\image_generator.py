import os
import random
from PIL import Image, ImageDraw, ImageFont
from typing import Tuple, Optional
import arabic_reshaper
from bidi.algorithm import get_display
import textwrap

class ImageGenerator:
    """مولد الصور مع الآيات القرآنية والخلفيات"""
    
    def __init__(self, 
                 backgrounds_dir: str = "backgrounds",
                 fonts_dir: str = "fonts",
                 output_dir: str = "generated_images"):
        self.backgrounds_dir = backgrounds_dir
        self.fonts_dir = fonts_dir
        self.output_dir = output_dir
        
        # إنشاء مجلد الإخراج إذا لم يكن موجوداً
        os.makedirs(output_dir, exist_ok=True)
        
        # الألوان المتاحة للنص
        self.text_colors = [
            (255, 255, 255),  # أبيض
            (240, 240, 240),  # أبيض مائل للرمادي
            (255, 215, 0),    # ذهبي
            (255, 255, 224),  # أبيض كريمي
            (245, 245, 220),  # بيج فاتح
        ]
        
        # أحجام الخط
        self.font_sizes = {
            'verse': 48,      # حجم خط الآية
            'surah': 32,      # حجم خط اسم السورة
            'phrase': 36      # حجم خط العبارة الإلهامية
        }
    
    def get_random_background(self) -> Optional[str]:
        """الحصول على خلفية عشوائية"""
        if not os.path.exists(self.backgrounds_dir):
            return None
        
        backgrounds = [f for f in os.listdir(self.backgrounds_dir) 
                      if f.lower().endswith(('.png', '.jpg', '.jpeg'))]
        
        if backgrounds:
            return os.path.join(self.backgrounds_dir, random.choice(backgrounds))
        return None
    
    def get_random_font(self) -> Optional[str]:
        """الحصول على خط عشوائي"""
        if not os.path.exists(self.fonts_dir):
            return None
        
        fonts = [f for f in os.listdir(self.fonts_dir) 
                if f.lower().endswith(('.ttf', '.otf'))]
        
        if fonts:
            return os.path.join(self.fonts_dir, random.choice(fonts))
        return None
    
    def create_default_background(self, size: Tuple[int, int] = (1080, 1080)) -> Image.Image:
        """إنشاء خلفية افتراضية بتدرج لوني"""
        img = Image.new('RGB', size, color=(20, 30, 50))
        draw = ImageDraw.Draw(img)
        
        # إنشاء تدرج لوني بسيط
        for y in range(size[1]):
            color_ratio = y / size[1]
            r = int(20 + (60 - 20) * color_ratio)
            g = int(30 + (80 - 30) * color_ratio)
            b = int(50 + (120 - 50) * color_ratio)
            draw.line([(0, y), (size[0], y)], fill=(r, g, b))
        
        return img
    
    def prepare_arabic_text(self, text: str) -> str:
        """تحضير النص العربي للعرض الصحيح"""
        reshaped_text = arabic_reshaper.reshape(text)
        return get_display(reshaped_text)
    
    def wrap_arabic_text(self, text: str, font: ImageFont.ImageFont, max_width: int) -> list:
        """تقسيم النص العربي إلى أسطر متعددة"""
        words = text.split()
        lines = []
        current_line = ""
        
        for word in words:
            test_line = f"{current_line} {word}".strip()
            bbox = font.getbbox(self.prepare_arabic_text(test_line))
            text_width = bbox[2] - bbox[0]
            
            if text_width <= max_width:
                current_line = test_line
            else:
                if current_line:
                    lines.append(current_line)
                current_line = word
        
        if current_line:
            lines.append(current_line)
        
        return lines
    
    def get_text_position(self, img_size: Tuple[int, int], text_size: Tuple[int, int], 
                         position: str = "center") -> Tuple[int, int]:
        """حساب موضع النص"""
        img_width, img_height = img_size
        text_width, text_height = text_size
        
        if position == "center":
            x = (img_width - text_width) // 2
            y = (img_height - text_height) // 2
        elif position == "top":
            x = (img_width - text_width) // 2
            y = img_height // 4
        elif position == "bottom":
            x = (img_width - text_width) // 2
            y = img_height - img_height // 4 - text_height
        else:
            x = (img_width - text_width) // 2
            y = (img_height - text_height) // 2
        
        return (x, y)
    
    def add_text_shadow(self, draw: ImageDraw.Draw, text: str, position: Tuple[int, int], 
                       font: ImageFont.ImageFont, text_color: Tuple[int, int, int]):
        """إضافة ظل للنص"""
        shadow_color = (0, 0, 0, 128)  # أسود شفاف
        shadow_offset = 3
        
        # رسم الظل
        shadow_pos = (position[0] + shadow_offset, position[1] + shadow_offset)
        draw.text(shadow_pos, text, font=font, fill=shadow_color)
        
        # رسم النص الأساسي
        draw.text(position, text, font=font, fill=text_color)
    
    def generate_verse_image(self, verse_data: dict, inspirational_phrase: str = "") -> str:
        """توليد صورة للآية القرآنية"""
        # الحصول على خلفية
        background_path = self.get_random_background()
        
        if background_path and os.path.exists(background_path):
            try:
                img = Image.open(background_path)
                img = img.resize((1080, 1080), Image.Resampling.LANCZOS)
            except Exception as e:
                print(f"خطأ في تحميل الخلفية: {e}")
                img = self.create_default_background()
        else:
            img = self.create_default_background()
        
        draw = ImageDraw.Draw(img)
        
        # الحصول على خط
        font_path = self.get_random_font()
        
        try:
            if font_path and os.path.exists(font_path):
                verse_font = ImageFont.truetype(font_path, self.font_sizes['verse'])
                surah_font = ImageFont.truetype(font_path, self.font_sizes['surah'])
                phrase_font = ImageFont.truetype(font_path, self.font_sizes['phrase'])
            else:
                # استخدام خط افتراضي
                verse_font = ImageFont.load_default()
                surah_font = ImageFont.load_default()
                phrase_font = ImageFont.load_default()
        except Exception as e:
            print(f"خطأ في تحميل الخط: {e}")
            verse_font = ImageFont.load_default()
            surah_font = ImageFont.load_default()
            phrase_font = ImageFont.load_default()
        
        # تحضير النصوص
        verse_text = verse_data['text']
        surah_info = f"سورة {verse_data['surah']} - آية {verse_data['verse_number']}"
        
        # تقسيم النص إلى أسطر
        max_width = img.width - 100  # هامش 50 بكسل من كل جانب
        verse_lines = self.wrap_arabic_text(verse_text, verse_font, max_width)
        
        # حساب الارتفاع الإجمالي للنص
        line_height = verse_font.getbbox("أ")[3] - verse_font.getbbox("أ")[1] + 10
        total_verse_height = len(verse_lines) * line_height
        
        surah_height = surah_font.getbbox("أ")[3] - surah_font.getbbox("أ")[1]
        phrase_height = phrase_font.getbbox("أ")[3] - phrase_font.getbbox("أ")[1] if inspirational_phrase else 0
        
        total_height = total_verse_height + surah_height + phrase_height + 60  # مسافات إضافية
        
        # موضع البداية
        start_y = (img.height - total_height) // 2
        
        # لون النص
        text_color = random.choice(self.text_colors)
        
        # رسم الآية
        current_y = start_y
        for line in verse_lines:
            prepared_line = self.prepare_arabic_text(line)
            bbox = verse_font.getbbox(prepared_line)
            text_width = bbox[2] - bbox[0]
            x = (img.width - text_width) // 2
            
            self.add_text_shadow(draw, prepared_line, (x, current_y), verse_font, text_color)
            current_y += line_height
        
        # رسم معلومات السورة
        current_y += 30
        prepared_surah = self.prepare_arabic_text(surah_info)
        bbox = surah_font.getbbox(prepared_surah)
        text_width = bbox[2] - bbox[0]
        x = (img.width - text_width) // 2
        
        self.add_text_shadow(draw, prepared_surah, (x, current_y), surah_font, text_color)
        
        # رسم العبارة الإلهامية إذا كانت موجودة
        if inspirational_phrase:
            current_y += surah_height + 30
            prepared_phrase = self.prepare_arabic_text(inspirational_phrase)
            bbox = phrase_font.getbbox(prepared_phrase)
            text_width = bbox[2] - bbox[0]
            x = (img.width - text_width) // 2
            
            # لون مختلف للعبارة الإلهامية
            phrase_color = (255, 215, 0)  # ذهبي
            self.add_text_shadow(draw, prepared_phrase, (x, current_y), phrase_font, phrase_color)
        
        # حفظ الصورة
        timestamp = int(os.path.getmtime(__file__) if os.path.exists(__file__) else 0)
        filename = f"verse_{verse_data['id']}_{timestamp}.png"
        output_path = os.path.join(self.output_dir, filename)
        
        img.save(output_path, "PNG", quality=95)
        return output_path
