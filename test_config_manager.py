#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار مدير الإعدادات الجديد
"""

import sys
import os

# إعداد الترميز للعربية
sys.stdout.reconfigure(encoding='utf-8')
sys.stderr.reconfigure(encoding='utf-8')

# إضافة مجلد المشروع للمسار
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.config_manager import ConfigManager

def test_config_manager():
    """اختبار مدير الإعدادات"""
    print("🧪 اختبار مدير الإعدادات")
    print("=" * 50)
    
    # إنشاء مدير الإعدادات
    config_manager = ConfigManager()
    
    # اختبار قراءة الإعدادات الحالية
    print("\n📋 الإعدادات الحالية:")
    current_config = config_manager.get_current_config()
    for key, value in current_config.items():
        if any(sensitive in key.upper() for sensitive in ['TOKEN', 'PASSWORD', 'KEY']):
            display_value = value[:10] + "..." + value[-5:] if value and len(value) > 15 else value
        else:
            display_value = value
        print(f"  {key}: {display_value}")
    
    # اختبار التحقق من صحة الإعدادات
    print("\n🔍 التحقق من صحة الإعدادات:")
    validation = config_manager.validate_config()
    
    if validation['errors']:
        print("❌ أخطاء:")
        for error in validation['errors']:
            print(f"  • {error}")
    
    if validation['warnings']:
        print("⚠️ تحذيرات:")
        for warning in validation['warnings']:
            print(f"  • {warning}")
    
    if not validation['errors'] and not validation['warnings']:
        print("✅ جميع الإعدادات صحيحة!")
    
    # اختبار ملخص الإعدادات
    print("\n📊 ملخص الإعدادات:")
    summary = config_manager.get_config_summary()
    print(summary)
    
    # اختبار تاريخ التغييرات
    print("\n📜 تاريخ التغييرات:")
    history = config_manager.get_config_history(5)
    if history:
        for i, entry in enumerate(reversed(history), 1):
            timestamp = entry.get('timestamp', 'غير محدد')
            user_id = entry.get('user_id', 'غير محدد')
            key = entry.get('key', 'غير محدد')
            print(f"  {i}. {key} - {timestamp[:19]} - المستخدم: {user_id}")
    else:
        print("  لا توجد تغييرات مسجلة")
    
    # اختبار تصدير الإعدادات
    print("\n📤 تصدير الإعدادات:")
    exported = config_manager.export_config(include_sensitive=False)
    print(f"  تم التصدير في: {exported['exported_at']}")
    print(f"  عدد الإعدادات: {len(exported['config'])}")
    
    print("\n✅ انتهى الاختبار بنجاح!")

if __name__ == "__main__":
    try:
        test_config_manager()
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()
