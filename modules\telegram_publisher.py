import os
import asyncio
from telegram import Bo<PERSON>
from telegram.error import TelegramError
from typing import Optional, Dict

class TelegramPublisher:
    """ناشر المحتوى على Telegram"""
    
    def __init__(self, bot_token: Optional[str] = None, channel_id: Optional[str] = None):
        self.bot_token = bot_token or os.getenv('TELEGRAM_BOT_TOKEN')
        self.channel_id = channel_id or os.getenv('TELEGRAM_CHANNEL_ID')

        if not self.bot_token:
            raise ValueError("رمز Telegram Bot مطلوب")
        if not self.channel_id:
            raise ValueError("معرف قناة Telegram مطلوب")

        # تنظيف معرف القناة من الرابط إذا كان موجوداً
        self.channel_id = self._extract_channel_username(self.channel_id)

        self.bot = Bot(token=self.bot_token)

    def _extract_channel_username(self, channel_input: str) -> str:
        """استخراج اسم المستخدم من رابط القناة أو المعرف"""
        if not channel_input:
            return channel_input

        # إزالة المسافات
        channel_input = channel_input.strip()

        # إذا كان رابط كامل
        if channel_input.startswith('https://t.me/'):
            username = channel_input.replace('https://t.me/', '')
            return f"@{username}" if not username.startswith('@') else username

        # إذا كان يبدأ بـ t.me/
        elif channel_input.startswith('t.me/'):
            username = channel_input.replace('t.me/', '')
            return f"@{username}" if not username.startswith('@') else username

        # إذا كان اسم مستخدم فقط بدون @
        elif not channel_input.startswith('@') and not channel_input.startswith('-'):
            return f"@{channel_input}"

        # إذا كان معرف صحيح بالفعل
        return channel_input
    
    async def send_image_with_caption(self, image_path: str, caption: str) -> bool:
        """إرسال صورة مع تعليق إلى القناة"""
        try:
            with open(image_path, 'rb') as photo:
                await self.bot.send_photo(
                    chat_id=self.channel_id,
                    photo=photo,
                    caption=caption,
                    parse_mode='HTML'
                )
            print(f"تم نشر الصورة بنجاح على Telegram: {image_path}")
            return True
            
        except TelegramError as e:
            print(f"خطأ في نشر الصورة على Telegram: {e}")
            return False
        except FileNotFoundError:
            print(f"الصورة غير موجودة: {image_path}")
            return False
        except Exception as e:
            print(f"خطأ غير متوقع في Telegram: {e}")
            return False
    
    async def send_text_message(self, text: str) -> bool:
        """إرسال رسالة نصية إلى القناة"""
        try:
            await self.bot.send_message(
                chat_id=self.channel_id,
                text=text,
                parse_mode='HTML'
            )
            print("تم إرسال الرسالة النصية بنجاح على Telegram")
            return True
            
        except TelegramError as e:
            print(f"خطأ في إرسال الرسالة على Telegram: {e}")
            return False
        except Exception as e:
            print(f"خطأ غير متوقع في Telegram: {e}")
            return False
    
    def publish_verse_content(self, image_path: str, content: Dict) -> bool:
        """نشر محتوى الآية (صورة + تعليق)"""
        # تحضير التعليق
        verse_data = content['verse']
        phrase = content['phrase']
        caption = content.get('caption', '')
        
        # إنشاء تعليق افتراضي إذا لم يكن متوفراً
        if not caption:
            caption = f"""
🕌 <b>{phrase}</b>

📖 <i>"{verse_data['text']}"</i>

📚 سورة {verse_data['surah']} - آية {verse_data['verse_number']}

#قرآن #ذكر #دعاء #أمل #طمأنينة #إسلام
            """.strip()
        
        # تشغيل النشر بشكل متزامن
        try:
            loop = asyncio.get_event_loop()
        except RuntimeError:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
        
        return loop.run_until_complete(
            self.send_image_with_caption(image_path, caption)
        )
    
    async def test_connection(self) -> bool:
        """اختبار الاتصال بـ Telegram"""
        try:
            bot_info = await self.bot.get_me()
            print(f"تم الاتصال بنجاح مع البوت: {bot_info.username}")
            return True
        except Exception as e:
            print(f"فشل في الاتصال مع Telegram: {e}")
            return False
    
    def format_verse_caption(self, verse_data: Dict, phrase: str, hashtags: list = None) -> str:
        """تنسيق تعليق الآية"""
        if hashtags is None:
            hashtags = ['#قرآن', '#ذكر', '#دعاء', '#أمل', '#طمأنينة', '#إسلام']
        
        caption = f"""
🕌 <b>{phrase}</b>

📖 <i>"{verse_data['text']}"</i>

📚 سورة {verse_data['surah']} - آية {verse_data['verse_number']}

{' '.join(hashtags)}
        """.strip()
        
        return caption
