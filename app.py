from flask import Flask, jsonify, render_template, request, session, redirect, url_for, send_file
import os
import logging
from datetime import datetime
import threading
import time
from functools import wraps

# استيراد الوحدات المخصصة
from config import get_config
from modules.quran_manager import QuranManager
from modules.image_generator import ImageGenerator
from modules.gemini_client import AdvancedGeminiClient
from modules.telegram_publisher import TelegramPublisher
from modules.instagram_publisher import InstagramPublisher
from modules.facebook_publisher import FacebookPublisher
from modules.scheduler import FlaskScheduler
from modules.user_manager import UserManager
from modules.template_manager import TemplateManager
from modules.analytics_manager import AnalyticsManager
from modules.security_manager import SecurityManager
from modules.unique_features import UniqueFeatures

# إعداد التطبيق
app = Flask(__name__)
config = get_config()
app.config.from_object(config)

# إعداد نظام السجلات
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# المتغيرات العامة
quran_manager = None
image_generator = None
gemini_client = None
publishers = {}
scheduler = None
user_manager = None
template_manager = None
analytics_manager = None
security_manager = None
unique_features = None

# Decorator للتحقق من تسجيل الدخول
def login_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'user_token' not in session:
            return redirect(url_for('login'))

        # التحقق من صحة الرمز
        user_data = security_manager.validate_token(session['user_token'], request.remote_addr)
        if not user_data:
            session.pop('user_token', None)
            return redirect(url_for('login'))

        return f(*args, **kwargs)
    return decorated_function

# Decorator للتحقق من صلاحيات المشرف
def admin_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'user_token' not in session:
            return redirect(url_for('login'))

        user_data = security_manager.validate_token(session['user_token'], request.remote_addr)
        if not user_data or user_data.get('role') != 'admin':
            return jsonify({'error': 'غير مصرح لك بالوصول'}), 403

        return f(*args, **kwargs)
    return decorated_function

def initialize_components():
    """تهيئة جميع مكونات التطبيق"""
    global quran_manager, image_generator, gemini_client, publishers, scheduler
    global user_manager, template_manager, analytics_manager, security_manager, unique_features

    try:
        # تهيئة مدير الأمان أولاً
        security_manager = SecurityManager()
        logger.info("تم تهيئة مدير الأمان بنجاح")

        # تهيئة مدير المستخدمين
        user_manager = UserManager()
        logger.info("تم تهيئة مدير المستخدمين بنجاح")

        # تهيئة مدير القرآن
        quran_manager = QuranManager()
        logger.info("تم تهيئة مدير القرآن بنجاح")

        # تهيئة مدير القوالب
        template_manager = TemplateManager()
        logger.info("تم تهيئة مدير القوالب بنجاح")

        # تهيئة مولد الصور المحسن
        image_generator = ImageGenerator()
        logger.info("تم تهيئة مولد الصور بنجاح")

        # تهيئة عميل Gemini المتقدم
        if config.GEMINI_API_KEY:
            gemini_client = AdvancedGeminiClient()
            logger.info("تم تهيئة عميل Gemini المتقدم بنجاح")
        else:
            logger.warning("مفتاح Gemini API غير متوفر")
        
        # تهيئة ناشري المنصات
        if config.ENABLE_TELEGRAM and config.TELEGRAM_BOT_TOKEN:
            try:
                publishers['telegram'] = TelegramPublisher()
                logger.info("تم تهيئة ناشر Telegram بنجاح")
            except Exception as e:
                logger.error(f"فشل في تهيئة ناشر Telegram: {e}")
        
        if config.ENABLE_INSTAGRAM and config.INSTAGRAM_USERNAME:
            try:
                publishers['instagram'] = InstagramPublisher()
                logger.info("تم تهيئة ناشر Instagram بنجاح")
            except Exception as e:
                logger.error(f"فشل في تهيئة ناشر Instagram: {e}")
        
        if config.ENABLE_FACEBOOK and config.FACEBOOK_ACCESS_TOKEN:
            try:
                publishers['facebook'] = FacebookPublisher()
                logger.info("تم تهيئة ناشر Facebook بنجاح")
            except Exception as e:
                logger.error(f"فشل في تهيئة ناشر Facebook: {e}")
        
        # تهيئة مدير التحليلات
        analytics_manager = AnalyticsManager()
        logger.info("تم تهيئة مدير التحليلات بنجاح")

        # تهيئة المميزات الفريدة
        unique_features = UniqueFeatures()
        logger.info("تم تهيئة المميزات الفريدة بنجاح")

        # تهيئة الجدولة
        scheduler = FlaskScheduler(publish_content, config.PUBLISH_INTERVAL_HOURS)
        logger.info("تم تهيئة نظام الجدولة بنجاح")

        logger.info("تم تهيئة جميع المكونات بنجاح")
        return True

    except Exception as e:
        logger.error(f"خطأ في تهيئة المكونات: {e}")
        return False

def publish_content():
    """نشر المحتوى على جميع المنصات"""
    try:
        logger.info("بدء عملية نشر المحتوى...")
        
        # الحصول على آية عشوائية
        verse = quran_manager.get_random_verse()
        if not verse:
            logger.error("لم يتم العثور على آية للنشر")
            return False
        
        # الحصول على عبارة إلهامية
        if gemini_client:
            phrase = gemini_client.get_enhanced_phrase()
        else:
            phrase = quran_manager.get_random_inspirational_phrase()
        
        # توليد الصورة
        image_path = image_generator.generate_verse_image(verse, phrase)
        if not image_path or not os.path.exists(image_path):
            logger.error("فشل في توليد الصورة")
            return False
        
        # تحضير المحتوى
        content = {
            'verse': verse,
            'phrase': phrase,
            'caption': None
        }
        
        # تحسين المحتوى باستخدام Gemini إذا كان متوفراً
        if gemini_client:
            try:
                enhanced_content = gemini_client.enhance_content(verse, phrase)
                content.update(enhanced_content)
            except Exception as e:
                logger.warning(f"فشل في تحسين المحتوى: {e}")
        
        # النشر على المنصات
        success_count = 0
        total_platforms = len(publishers)
        
        for platform_name, publisher in publishers.items():
            try:
                success = publisher.publish_verse_content(image_path, content)
                if success:
                    success_count += 1
                    logger.info(f"تم النشر بنجاح على {platform_name}")
                else:
                    logger.error(f"فشل النشر على {platform_name}")
            except Exception as e:
                logger.error(f"خطأ في النشر على {platform_name}: {e}")
        
        # تنظيف الصورة المؤقتة (اختياري)
        try:
            if os.path.exists(image_path):
                os.remove(image_path)
        except Exception as e:
            logger.warning(f"فشل في حذف الصورة المؤقتة: {e}")
        
        logger.info(f"تم النشر على {success_count}/{total_platforms} منصات")
        return success_count > 0
        
    except Exception as e:
        logger.error(f"خطأ في عملية النشر: {e}")
        return False

# طرق المصادقة والأمان
@app.route('/login', methods=['GET', 'POST'])
def login():
    """صفحة تسجيل الدخول"""
    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')

        if not username or not password:
            return render_template('login.html', error='يرجى إدخال اسم المستخدم وكلمة المرور')

        # التحقق من IP المحظور
        if not security_manager.check_ip_allowed(request.remote_addr):
            return render_template('login.html', error='عذراً، تم حظر عنوان IP الخاص بك')

        # محاولة تسجيل الدخول
        user_data = user_manager.authenticate_user(username, password)

        # تسجيل المحاولة
        security_manager.record_login_attempt(
            username,
            request.remote_addr,
            user_data is not None,
            request.headers.get('User-Agent', '')
        )

        if user_data:
            # إنشاء رمز آمن
            token = security_manager.generate_secure_token(
                user_data['user_id'],
                {'ip_address': request.remote_addr}
            )
            session['user_token'] = token
            session['user_id'] = user_data['user_id']
            session['username'] = user_data['username']

            return redirect(url_for('dashboard'))
        else:
            return render_template('login.html', error='اسم المستخدم أو كلمة المرور غير صحيحة')

    return render_template('login.html')

@app.route('/logout')
def logout():
    """تسجيل الخروج"""
    if 'user_token' in session:
        security_manager.revoke_token(session['user_token'])

    session.clear()
    return redirect(url_for('login'))

@app.route('/')
@login_required
def home():
    """الصفحة الرئيسية - إعادة توجيه للوحة التحكم"""
    return redirect(url_for('dashboard'))

@app.route('/dashboard')
@login_required
def dashboard():
    """لوحة التحكم الرئيسية"""
    user_id = session.get('user_id')
    username = session.get('username')

    # الحصول على الإحصائيات
    stats = analytics_manager.get_performance_summary() if analytics_manager else {}

    # الحصول على وقت النشر التالي
    scheduler_status = scheduler.get_status() if scheduler else {}
    next_post_time = scheduler_status.get('time_until_next_run_seconds', 0)

    if next_post_time > 0:
        hours = next_post_time // 3600
        minutes = (next_post_time % 3600) // 60
        next_post_display = f"{hours}س {minutes}د"
    else:
        next_post_display = "قريباً"

    return render_template('dashboard.html',
                         username=username,
                         stats=stats,
                         next_post_time=next_post_display)

@app.route('/status')
def status():
    """حالة النظام التفصيلية"""
    try:
        # إحصائيات القرآن
        verse_stats = quran_manager.get_verse_stats() if quran_manager else {}
        
        # حالة الجدولة
        scheduler_status = scheduler.get_status() if scheduler else {}
        
        # حالة المنصات
        platform_status = {}
        for name, publisher in publishers.items():
            try:
                if hasattr(publisher, 'test_connection'):
                    platform_status[name] = 'متصل' if publisher.test_connection() else 'غير متصل'
                else:
                    platform_status[name] = 'مُعد'
            except:
                platform_status[name] = 'خطأ'
        
        return jsonify({
            'system_status': 'يعمل',
            'timestamp': datetime.now().isoformat(),
            'verse_stats': verse_stats,
            'scheduler_status': scheduler_status,
            'platform_status': platform_status,
            'config': {
                'environment': config.ENVIRONMENT,
                'interval_hours': config.PUBLISH_INTERVAL_HOURS,
                'enabled_platforms': config.get_enabled_platforms()
            }
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/test')
def test_publish():
    """اختبار النشر الفوري"""
    try:
        success = publish_content()
        return jsonify({
            'success': success,
            'message': 'تم النشر بنجاح' if success else 'فشل في النشر',
            'timestamp': datetime.now().isoformat()
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500

# API Endpoints
@app.route('/api/dashboard-stats')
@login_required
def api_dashboard_stats():
    """إحصائيات لوحة التحكم"""
    user_id = session.get('user_id')

    try:
        # إحصائيات الأداء
        performance_summary = analytics_manager.get_performance_summary() if analytics_manager else {}

        # إحصائيات القرآن
        verse_stats = quran_manager.get_verse_stats() if quran_manager else {}

        # حالة الجدولة
        scheduler_status = scheduler.get_status() if scheduler else {}

        return jsonify({
            'total_posts': performance_summary.get('total_posts', 0),
            'success_rate': performance_summary.get('success_rate', 0),
            'avg_engagement': performance_summary.get('avg_performance_score', 0),
            'next_post_time': scheduler_status.get('time_until_next_run_seconds', 0),
            'verse_stats': verse_stats
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/platform-status')
@login_required
def api_platform_status():
    """حالة المنصات"""
    try:
        platform_status = {}

        for platform_name, publisher in publishers.items():
            try:
                # محاولة اختبار الاتصال
                if hasattr(publisher, 'test_connection'):
                    is_connected = publisher.test_connection()
                else:
                    is_connected = True  # افتراض الاتصال إذا لم يكن هناك اختبار

                platform_status[platform_name] = {
                    'status': 'active' if is_connected else 'inactive',
                    'posts': 0  # سيتم تحديثه من التحليلات
                }
            except Exception:
                platform_status[platform_name] = {
                    'status': 'inactive',
                    'posts': 0
                }

        return jsonify(platform_status)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/recent-posts')
@login_required
def api_recent_posts():
    """المنشورات الأخيرة"""
    try:
        if analytics_manager:
            # الحصول على آخر 10 منشورات
            all_posts = analytics_manager.analytics_data.get('posts', [])
            recent_posts = sorted(all_posts, key=lambda x: x['timestamp'], reverse=True)[:10]

            return jsonify({'posts': recent_posts})
        else:
            return jsonify({'posts': []})
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/content-insights')
@login_required
def api_content_insights():
    """رؤى المحتوى"""
    try:
        if analytics_manager:
            insights = analytics_manager.get_content_insights()
            return jsonify(insights)
        else:
            return jsonify({'error': 'مدير التحليلات غير متوفر'})
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/performance-data')
@login_required
def api_performance_data():
    """بيانات الأداء للرسم البياني"""
    try:
        if analytics_manager:
            all_posts = analytics_manager.analytics_data.get('posts', [])

            # أخذ آخر 30 منشور
            recent_posts = sorted(all_posts, key=lambda x: x['timestamp'])[-30:]

            labels = []
            performance_scores = []

            for post in recent_posts:
                # تنسيق التاريخ
                post_date = datetime.fromisoformat(post['timestamp'])
                labels.append(post_date.strftime('%m/%d'))
                performance_scores.append(post.get('performance_score', 0))

            return jsonify({
                'labels': labels,
                'performance_scores': performance_scores
            })
        else:
            return jsonify({'labels': [], 'performance_scores': []})
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/publish-now', methods=['POST'])
@login_required
def api_publish_now():
    """نشر فوري"""
    try:
        success = publish_content()

        if success:
            return jsonify({'success': True, 'message': 'تم النشر بنجاح'})
        else:
            return jsonify({'success': False, 'message': 'فشل في النشر'})
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500

@app.route('/api/preview-post')
@login_required
def api_preview_post():
    """معاينة المنشور"""
    try:
        # الحصول على آية عشوائية
        verse = quran_manager.get_random_verse() if quran_manager else None
        if not verse:
            return jsonify({'success': False, 'message': 'لم يتم العثور على آية'})

        # الحصول على عبارة إلهامية
        if gemini_client:
            phrase = gemini_client.get_enhanced_phrase()
        else:
            phrase = quran_manager.get_random_inspirational_phrase() if quran_manager else "اللهم بارك لنا"

        # توليد الصورة
        if image_generator:
            image_path = image_generator.generate_verse_image(verse, phrase)

            if image_path and os.path.exists(image_path):
                return jsonify({
                    'success': True,
                    'preview_url': f'/api/preview-image/{os.path.basename(image_path)}',
                    'verse': verse,
                    'phrase': phrase
                })

        return jsonify({'success': False, 'message': 'فشل في إنشاء المعاينة'})
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500

@app.route('/api/preview-image/<filename>')
@login_required
def api_preview_image(filename):
    """عرض صورة المعاينة"""
    try:
        image_path = os.path.join('generated_images', filename)
        if os.path.exists(image_path):
            return send_file(image_path, mimetype='image/png')
        else:
            return jsonify({'error': 'الصورة غير موجودة'}), 404
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/export-report', methods=['POST'])
@login_required
def api_export_report():
    """تصدير تقرير التحليلات"""
    try:
        if analytics_manager:
            report_file = analytics_manager.export_analytics_report('json')
            return send_file(report_file, as_attachment=True, download_name=f'analytics_report_{datetime.now().strftime("%Y%m%d")}.json')
        else:
            return jsonify({'error': 'مدير التحليلات غير متوفر'}), 500
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/health')
def health_check():
    """فحص صحة التطبيق (مطلوب لـ Render)"""
    return jsonify({'status': 'healthy', 'timestamp': datetime.now().isoformat()})

def background_scheduler():
    """تشغيل الجدولة في الخلفية"""
    while True:
        try:
            if scheduler:
                scheduler.check_and_run()
            time.sleep(300)  # فحص كل 5 دقائق
        except Exception as e:
            logger.error(f"خطأ في الجدولة الخلفية: {e}")
            time.sleep(300)

if __name__ == '__main__':
    # تهيئة المكونات
    if initialize_components():
        # بدء الجدولة في خيط منفصل
        scheduler_thread = threading.Thread(target=background_scheduler, daemon=True)
        scheduler_thread.start()
        
        # طباعة ملخص الإعدادات
        config.print_config_summary()
        
        # تشغيل التطبيق
        app.run(host='0.0.0.0', port=config.PORT, debug=False)
    else:
        logger.error("فشل في تهيئة التطبيق")
        exit(1)
