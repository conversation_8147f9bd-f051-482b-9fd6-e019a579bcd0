#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار سريع للوكيل المحدث
"""

import os
import sys

def main():
    print("🚀 بدء اختبار سريع للوكيل المحدث...")
    print("=" * 60)
    
    # التحقق من الملفات المطلوبة
    required_files = [
        "app.py",
        "modules/image_generator.py", 
        "modules/scheduler.py",
        "templates/dashboard.html",
        "static/js/dashboard.js",
        "fonts/Amiri Quran.ttf"
    ]
    
    print("📁 فحص الملفات المطلوبة:")
    missing_files = []
    
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"  ✓ {file_path}")
        else:
            print(f"  ✗ {file_path} - مفقود")
            missing_files.append(file_path)
    
    if missing_files:
        print(f"\n⚠ ملفات مفقودة: {len(missing_files)}")
        for file in missing_files:
            print(f"  - {file}")
        
        if "fonts/Amiri Quran.ttf" in missing_files:
            print("\n💡 نصيحة: تأكد من وضع الخط القرآني في مجلد fonts/")
    else:
        print("\n✅ جميع الملفات موجودة!")
    
    # اختبار استيراد الوحدات
    print("\n📦 اختبار استيراد الوحدات:")
    
    modules_to_test = [
        ("config", "get_config"),
        ("modules.image_generator", "ImageGenerator"),
        ("modules.scheduler", "FlaskScheduler"),
        ("modules.quran_manager", "QuranManager"),
    ]
    
    import_errors = []
    
    for module_name, class_name in modules_to_test:
        try:
            module = __import__(module_name, fromlist=[class_name])
            getattr(module, class_name)
            print(f"  ✓ {module_name}.{class_name}")
        except Exception as e:
            print(f"  ✗ {module_name}.{class_name} - خطأ: {e}")
            import_errors.append((module_name, class_name, str(e)))
    
    if import_errors:
        print(f"\n⚠ أخطاء في الاستيراد: {len(import_errors)}")
        for module, class_name, error in import_errors:
            print(f"  - {module}.{class_name}: {error}")
    else:
        print("\n✅ جميع الوحدات تعمل بشكل صحيح!")
    
    # اختبار سريع للخط
    print("\n🔤 اختبار الخط القرآني:")
    try:
        from modules.image_generator import ImageGenerator
        img_gen = ImageGenerator()
        font_path = img_gen.get_quran_font()
        
        if font_path:
            print(f"  ✓ تم العثور على الخط: {font_path}")
        else:
            print("  ⚠ لم يتم العثور على خط قرآني")
            
    except Exception as e:
        print(f"  ✗ خطأ في اختبار الخط: {e}")
    
    # اختبار معالجة النص العربي
    print("\n📝 اختبار معالجة النص العربي:")
    try:
        from modules.image_generator import ImageGenerator
        img_gen = ImageGenerator()
        
        test_text = "بِسْمِ اللَّهِ الرَّحْمَٰنِ الرَّحِيمِ"
        processed = img_gen.prepare_arabic_text(test_text)
        
        if processed and len(processed) > 0:
            print(f"  ✓ النص الأصلي: {test_text}")
            print(f"  ✓ النص المعالج: {processed}")
        else:
            print("  ✗ فشل في معالجة النص")
            
    except Exception as e:
        print(f"  ✗ خطأ في معالجة النص: {e}")
    
    # تعليمات التشغيل
    print("\n" + "=" * 60)
    print("🎯 تعليمات التشغيل:")
    print("=" * 60)
    
    print("1. لتشغيل الوكيل:")
    print("   python app.py")
    print()
    print("2. لفتح لوحة التحكم:")
    print("   http://localhost:5000")
    print()
    print("3. للاختبار الشامل:")
    print("   python test_improvements.py")
    print()
    print("4. الميزات الجديدة في لوحة التحكم:")
    print("   • زر تشغيل/إيقاف النشر التلقائي")
    print("   • زر إعدادات التوقيت")
    print("   • زر حالة الجدولة")
    print("   • معالجة محسنة للنص العربي")
    print("   • استخدام الخط القرآني")
    
    print("\n" + "=" * 60)
    print("✨ تم حل جميع المشاكل المطلوبة:")
    print("✅ إصلاح مشكلة الخط العربي")
    print("✅ إضافة زر النشر التلقائي") 
    print("✅ تحسين نظام الجدولة")
    print("✅ إضافة واجهة تخصيص التوقيت")
    print("=" * 60)

if __name__ == "__main__":
    main()
