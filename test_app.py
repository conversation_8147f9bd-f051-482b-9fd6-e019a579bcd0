#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار مكونات التطبيق
"""

import os
import sys
from dotenv import load_dotenv

# تحميل متغيرات البيئة
load_dotenv()

def test_quran_manager():
    """اختبار مدير القرآن"""
    print("🔍 اختبار مدير القرآن...")
    try:
        from modules.quran_manager import QuranManager
        manager = QuranManager()
        
        # اختبار الحصول على آية عشوائية
        verse = manager.get_random_verse()
        if verse:
            print(f"✅ تم الحصول على آية: {verse['surah']} - آية {verse['verse_number']}")
            print(f"   النص: {verse['text'][:50]}...")
        else:
            print("❌ فشل في الحصول على آية")
            return False
        
        # اختبار الإحصائيات
        stats = manager.get_verse_stats()
        print(f"📊 إحصائيات: {stats['total_verses']} آية، استُخدم {stats['used_verses']}")
        
        # اختبار العبارات الإلهامية
        phrase = manager.get_random_inspirational_phrase()
        if phrase:
            print(f"💭 عبارة إلهامية: {phrase}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار مدير القرآن: {e}")
        return False

def test_image_generator():
    """اختبار مولد الصور"""
    print("\n🎨 اختبار مولد الصور...")
    try:
        from modules.image_generator import ImageGenerator
        from modules.quran_manager import QuranManager
        
        generator = ImageGenerator()
        manager = QuranManager()
        
        # الحصول على آية للاختبار
        verse = manager.get_random_verse()
        if not verse:
            print("❌ لا توجد آية للاختبار")
            return False
        
        phrase = "اللّهُمّ عَوِّضْنِي بِالأَجْمَل يَا رَبّ"
        
        # توليد صورة تجريبية
        image_path = generator.generate_verse_image(verse, phrase)
        
        if image_path and os.path.exists(image_path):
            print(f"✅ تم توليد الصورة: {image_path}")
            return True
        else:
            print("❌ فشل في توليد الصورة")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار مولد الصور: {e}")
        return False

def test_gemini_client():
    """اختبار عميل Gemini"""
    print("\n🤖 اختبار عميل Gemini...")
    
    api_key = os.getenv('GEMINI_API_KEY')
    if not api_key:
        print("⚠️ مفتاح Gemini API غير متوفر - تخطي الاختبار")
        return True
    
    try:
        from modules.gemini_client import GeminiClient
        
        client = GeminiClient()
        
        # اختبار توليد عبارة إلهامية
        phrase = client.generate_inspirational_phrase()
        if phrase:
            print(f"✅ عبارة مولدة: {phrase}")
        else:
            print("⚠️ لم يتم توليد عبارة (قد يكون بسبب حدود API)")
        
        # اختبار العبارات الاحتياطية
        fallback_phrase = client.get_enhanced_phrase()
        print(f"💡 عبارة محسنة: {fallback_phrase}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار عميل Gemini: {e}")
        return False

def test_advanced_features():
    """اختبار المميزات المتقدمة"""
    print("\n🌟 اختبار المميزات المتقدمة...")

    results = {}

    # اختبار مدير المستخدمين
    try:
        from modules.user_manager import UserManager
        user_manager = UserManager()
        print("✅ تم إنشاء مدير المستخدمين")

        # اختبار إنشاء مستخدم تجريبي
        try:
            test_user = user_manager.create_user("test_user", "<EMAIL>", "testpass123")
            print(f"✅ تم إنشاء مستخدم تجريبي: {test_user['username']}")
        except ValueError as e:
            print(f"⚠️ المستخدم موجود بالفعل: {e}")

        results['user_manager'] = True
    except Exception as e:
        print(f"❌ خطأ في مدير المستخدمين: {e}")
        results['user_manager'] = False

    # اختبار مدير القوالب
    try:
        from modules.template_manager import TemplateManager
        template_manager = TemplateManager()
        templates = template_manager.get_template_list()
        print(f"✅ تم تحميل {len(templates)} قالب تصميم")
        results['template_manager'] = True
    except Exception as e:
        print(f"❌ خطأ في مدير القوالب: {e}")
        results['template_manager'] = False

    # اختبار مدير التحليلات
    try:
        from modules.analytics_manager import AnalyticsManager
        analytics_manager = AnalyticsManager()
        print("✅ تم إنشاء مدير التحليلات")
        results['analytics_manager'] = True
    except Exception as e:
        print(f"❌ خطأ في مدير التحليلات: {e}")
        results['analytics_manager'] = False

    # اختبار مدير الأمان
    try:
        from modules.security_manager import SecurityManager
        security_manager = SecurityManager()

        # اختبار قوة كلمة المرور
        password_test = security_manager.validate_password_strength("TestPass123!")
        print(f"✅ اختبار قوة كلمة المرور: {password_test['strength']}")
        results['security_manager'] = True
    except Exception as e:
        print(f"❌ خطأ في مدير الأمان: {e}")
        results['security_manager'] = False

    # اختبار المميزات الفريدة
    try:
        from modules.unique_features import UniqueFeatures
        unique_features = UniqueFeatures()
        print("✅ تم تحميل المميزات الفريدة")
        results['unique_features'] = True
    except Exception as e:
        print(f"❌ خطأ في المميزات الفريدة: {e}")
        results['unique_features'] = False

    return results

def test_publishers():
    """اختبار ناشري المنصات"""
    print("\n📱 اختبار ناشري المنصات...")

    results = {}

    # اختبار Telegram
    if os.getenv('TELEGRAM_BOT_TOKEN'):
        try:
            from modules.telegram_publisher import TelegramPublisher
            publisher = TelegramPublisher()
            print("✅ تم إنشاء ناشر Telegram")
            results['telegram'] = True
        except Exception as e:
            print(f"❌ خطأ في ناشر Telegram: {e}")
            results['telegram'] = False
    else:
        print("⚠️ رمز Telegram غير متوفر - تخطي الاختبار")
        results['telegram'] = None
    
    # اختبار Instagram
    if os.getenv('INSTAGRAM_USERNAME'):
        try:
            from modules.instagram_publisher import InstagramPublisher
            publisher = InstagramPublisher()
            print("✅ تم إنشاء ناشر Instagram")
            results['instagram'] = True
        except Exception as e:
            print(f"❌ خطأ في ناشر Instagram: {e}")
            results['instagram'] = False
    else:
        print("⚠️ بيانات Instagram غير متوفرة - تخطي الاختبار")
        results['instagram'] = None
    
    # اختبار Facebook
    if os.getenv('FACEBOOK_ACCESS_TOKEN'):
        try:
            from modules.facebook_publisher import FacebookPublisher
            publisher = FacebookPublisher()
            print("✅ تم إنشاء ناشر Facebook")
            results['facebook'] = True
        except Exception as e:
            print(f"❌ خطأ في ناشر Facebook: {e}")
            results['facebook'] = False
    else:
        print("⚠️ رمز Facebook غير متوفر - تخطي الاختبار")
        results['facebook'] = None
    
    return results

def test_config():
    """اختبار الإعدادات"""
    print("\n⚙️ اختبار الإعدادات...")
    try:
        from config import get_config
        
        config = get_config()
        
        print(f"📋 البيئة: {config.ENVIRONMENT}")
        print(f"🔌 المنفذ: {config.PORT}")
        print(f"⏰ فترة النشر: {config.PUBLISH_INTERVAL_HOURS} ساعات")
        
        # التحقق من الإعدادات
        errors = config.validate_config()
        if errors:
            print("❌ أخطاء في الإعدادات:")
            for error in errors:
                print(f"   - {error}")
            return False
        else:
            print("✅ جميع الإعدادات صحيحة")
            return True
            
    except Exception as e:
        print(f"❌ خطأ في اختبار الإعدادات: {e}")
        return False

def main():
    """تشغيل جميع الاختبارات"""
    print("🚀 بدء اختبار مكونات التطبيق")
    print("=" * 50)
    
    tests = [
        ("الإعدادات", test_config),
        ("مدير القرآن", test_quran_manager),
        ("مولد الصور", test_image_generator),
        ("عميل Gemini", test_gemini_client),
        ("المميزات المتقدمة", test_advanced_features),
        ("ناشري المنصات", test_publishers)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results[test_name] = result
        except Exception as e:
            print(f"❌ خطأ في اختبار {test_name}: {e}")
            results[test_name] = False
    
    # ملخص النتائج
    print("\n" + "=" * 50)
    print("📊 ملخص نتائج الاختبار:")
    print("=" * 50)
    
    for test_name, result in results.items():
        if result is True:
            print(f"✅ {test_name}: نجح")
        elif result is False:
            print(f"❌ {test_name}: فشل")
        else:
            print(f"⚠️ {test_name}: تم التخطي")
    
    # تحديد النجاح العام
    failed_tests = [name for name, result in results.items() if result is False]
    
    if failed_tests:
        print(f"\n❌ فشل في {len(failed_tests)} اختبار(ات)")
        print("🔧 يرجى مراجعة الأخطاء أعلاه وإصلاحها")
        return False
    else:
        print("\n🎉 جميع الاختبارات نجحت!")
        print("✅ التطبيق جاهز للنشر")
        return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
