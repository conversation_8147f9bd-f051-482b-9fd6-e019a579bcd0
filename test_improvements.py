#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار التحسينات الجديدة للوكيل
"""

import os
import sys
import json
from datetime import datetime

# إضافة المجلد الحالي للمسار
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_font_loading():
    """اختبار تحميل الخط القرآني"""
    print("=" * 50)
    print("اختبار تحميل الخط القرآني")
    print("=" * 50)
    
    try:
        from modules.image_generator import ImageGenerator
        
        # إنشاء مولد الصور
        image_gen = ImageGenerator()
        
        # اختبار الحصول على الخط القرآني
        font_path = image_gen.get_quran_font()
        
        if font_path and os.path.exists(font_path):
            print(f"✓ تم العثور على الخط القرآني: {font_path}")
            return True
        else:
            print("⚠ لم يتم العثور على الخط القرآني")
            return False
            
    except Exception as e:
        print(f"✗ خطأ في اختبار الخط: {e}")
        return False

def test_arabic_text_processing():
    """اختبار معالجة النص العربي"""
    print("\n" + "=" * 50)
    print("اختبار معالجة النص العربي")
    print("=" * 50)
    
    try:
        from modules.image_generator import ImageGenerator
        
        image_gen = ImageGenerator()
        
        # نص تجريبي
        test_text = "بِسْمِ اللَّهِ الرَّحْمَٰنِ الرَّحِيمِ"
        
        # معالجة النص
        processed_text = image_gen.prepare_arabic_text(test_text)
        
        print(f"النص الأصلي: {test_text}")
        print(f"النص المعالج: {processed_text}")
        
        if processed_text and len(processed_text) > 0:
            print("✓ تم معالجة النص العربي بنجاح")
            return True
        else:
            print("✗ فشل في معالجة النص العربي")
            return False
            
    except Exception as e:
        print(f"✗ خطأ في معالجة النص: {e}")
        return False

def test_scheduler_functionality():
    """اختبار وظائف الجدولة"""
    print("\n" + "=" * 50)
    print("اختبار وظائف الجدولة")
    print("=" * 50)
    
    try:
        from modules.scheduler import FlaskScheduler
        
        # دالة وهمية للنشر
        def dummy_publish():
            print("تم تشغيل النشر الوهمي")
            return True
        
        # إنشاء جدولة
        scheduler = FlaskScheduler(dummy_publish, 1)  # كل ساعة
        
        # اختبار الحالة
        status = scheduler.get_status()
        print(f"حالة الجدولة: {json.dumps(status, indent=2, ensure_ascii=False)}")
        
        # اختبار تحديث الفترة
        scheduler.update_interval(2)
        print("✓ تم تحديث فترة النشر إلى ساعتين")
        
        # اختبار التفعيل والتعطيل
        scheduler.disable_scheduler()
        print("✓ تم تعطيل الجدولة")
        
        scheduler.enable_scheduler()
        print("✓ تم تفعيل الجدولة")
        
        return True
        
    except Exception as e:
        print(f"✗ خطأ في اختبار الجدولة: {e}")
        return False

def test_image_generation():
    """اختبار توليد الصور"""
    print("\n" + "=" * 50)
    print("اختبار توليد الصور")
    print("=" * 50)
    
    try:
        from modules.image_generator import ImageGenerator
        
        image_gen = ImageGenerator()
        
        # بيانات آية تجريبية
        verse_data = {
            'id': 1,
            'text': 'بِسْمِ اللَّهِ الرَّحْمَٰنِ الرَّحِيمِ',
            'surah': 'الفاتحة',
            'verse_number': 1
        }
        
        # عبارة إلهامية
        phrase = 'اللهم بارك لنا'
        
        # توليد الصورة
        image_path = image_gen.generate_verse_image(verse_data, phrase)
        
        if image_path and os.path.exists(image_path):
            print(f"✓ تم توليد الصورة بنجاح: {image_path}")
            
            # فحص حجم الملف
            file_size = os.path.getsize(image_path)
            print(f"حجم الصورة: {file_size} بايت")
            
            return True
        else:
            print("✗ فشل في توليد الصورة")
            return False
            
    except Exception as e:
        print(f"✗ خطأ في توليد الصورة: {e}")
        return False

def test_config_loading():
    """اختبار تحميل الإعدادات"""
    print("\n" + "=" * 50)
    print("اختبار تحميل الإعدادات")
    print("=" * 50)
    
    try:
        from config import get_config
        
        config = get_config()
        
        print(f"البيئة: {config.ENVIRONMENT}")
        print(f"فترة النشر: {config.PUBLISH_INTERVAL_HOURS} ساعات")
        print(f"أبعاد الصورة: {config.IMAGE_WIDTH}x{config.IMAGE_HEIGHT}")
        print(f"المنصات المفعلة: {', '.join(config.get_enabled_platforms())}")
        
        print("✓ تم تحميل الإعدادات بنجاح")
        return True
        
    except Exception as e:
        print(f"✗ خطأ في تحميل الإعدادات: {e}")
        return False

def main():
    """تشغيل جميع الاختبارات"""
    print("بدء اختبار التحسينات الجديدة...")
    print(f"الوقت: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    tests = [
        ("تحميل الإعدادات", test_config_loading),
        ("تحميل الخط القرآني", test_font_loading),
        ("معالجة النص العربي", test_arabic_text_processing),
        ("وظائف الجدولة", test_scheduler_functionality),
        ("توليد الصور", test_image_generation),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"✗ خطأ في اختبار {test_name}: {e}")
            results.append((test_name, False))
    
    # ملخص النتائج
    print("\n" + "=" * 50)
    print("ملخص نتائج الاختبارات")
    print("=" * 50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✓ نجح" if result else "✗ فشل"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nالنتيجة النهائية: {passed}/{total} اختبارات نجحت")
    
    if passed == total:
        print("🎉 جميع الاختبارات نجحت!")
        return True
    else:
        print("⚠ بعض الاختبارات فشلت")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
