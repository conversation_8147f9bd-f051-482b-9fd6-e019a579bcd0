import google.generativeai as genai
import os
import random
import json
from datetime import datetime, timedelta
from typing import Optional, List, Dict, Any
import hashlib

class AdvancedGeminiClient:
    """عميل Gemini API متقدم لتوليد المحتوى الإسلامي المتنوع"""

    def __init__(self, api_key: Optional[str] = None, user_id: Optional[str] = None):
        self.api_key = api_key or os.getenv('GEMINI_API_KEY')
        if not self.api_key:
            raise ValueError("مفتاح Gemini API مطلوب")

        genai.configure(api_key=self.api_key)
        self.model = genai.GenerativeModel('gemini-pro')
        self.user_id = user_id or "default"

        # إعدادات الإبداع
        self.creativity_levels = {
            'low': {'temperature': 0.3, 'top_p': 0.8},
            'medium': {'temperature': 0.7, 'top_p': 0.9},
            'high': {'temperature': 0.9, 'top_p': 0.95}
        }

        # ذاكرة المحتوى المولد لتجنب التكرار
        self.content_memory = self._load_content_memory()

        # قوالب الطلبات المتقدمة
        self.advanced_prompts = {
            'contextual_phrase': """
            أنت خبير في الأدب الإسلامي والدعاء. أنشئ عبارة دعائية مؤثرة تناسب السياق التالي:

            الموضوع: {theme}
            الوقت: {time_context}
            المزاج المطلوب: {mood}
            الجمهور المستهدف: {audience}

            المتطلبات:
            - عبارة باللغة العربية الفصحى
            - مؤثرة وملهمة روحياً
            - تتناسب مع الموضوع والسياق
            - لا تزيد عن 12 كلمة
            - تجنب التكرار مع هذه العبارات السابقة: {previous_phrases}

            أنشئ عبارة واحدة فقط:
            """,

            'smart_verse_selection': """
            أنت عالم قرآن متخصص. اقترح آية قرآنية مناسبة للمعايير التالية:

            الموضوع الرئيسي: {main_theme}
            المواضيع الفرعية: {sub_themes}
            الهدف من المنشور: {purpose}
            مستوى التعقيد المطلوب: {complexity}

            يجب أن تكون الآية:
            - مرتبطة بقوة بالموضوع
            - مناسبة للجمهور العام
            - لها تأثير إيجابي ومحفز
            - لم تُستخدم مؤخراً (تجنب: {recent_verses})

            اذكر فقط: نص الآية، اسم السورة، رقم الآية، والسبب في اختيارها.
            """,

            'dynamic_caption': """
            أنت كاتب محتوى إبداعي متخصص في المحتوى الإسلامي. أنشئ تعليق جذاب لمنشور على وسائل التواصل:

            الآية: "{verse_text}"
            السورة: {surah_name} - آية {verse_number}
            العبارة الإلهامية: "{inspirational_phrase}"
            نمط المنشور: {post_style}
            المنصة: {platform}

            أنشئ تعليق يتضمن:
            - مقدمة جذابة تربط بالواقع
            - تفسير مبسط أو تأمل في الآية
            - دعوة للتفاعل أو التأمل
            - هاشتاغات مناسبة (5-8 هاشتاغات)
            - أسلوب يناسب المنصة المحددة

            الطول المطلوب: {caption_length} كلمة تقريباً
            """,

            'inspirational_phrase': """
            أنشئ عبارة دعائية إسلامية قصيرة وجذابة باللغة العربية تحتوي على:
            - دعاء جميل ومؤثر
            - كلمات تبث الأمل والطمأنينة
            - أسلوب بسيط ومفهوم
            - لا تزيد عن 15 كلمة
            
            أمثلة على النمط المطلوب:
            - "رَبِّي عَوِّضْنِي خَيْرًا عَنْ كُلِّ شَيْءٍ انْكَسَرَ بِنَفْسِي"
            - "اللّهُمّ أرِنِي الفَرَحَ بِمُسْتَقْبَلِي"
            - "اللّهُمّ عَوِّضْنِي بِالأَجْمَل يَا رَبّ"
            
            أنشئ عبارة جديدة مشابهة:
            """,
            
            'verse_suggestion': """
            اقترح آية قرآنية مناسبة للموضوع: {theme}
            
            يجب أن تكون الآية:
            - مرتبطة بالموضوع المطلوب
            - مفهومة ومؤثرة
            - تبث الأمل والطمأنينة
            
            اذكر فقط نص الآية مع اسم السورة ورقم الآية.
            """,
            
            'caption_for_social': """
            أنشئ تعليق (caption) لمنشور على وسائل التواصل الاجتماعي يحتوي على:
            
            الآية: {verse}
            العبارة الإلهامية: {phrase}
            
            التعليق يجب أن يكون:
            - مؤثر وملهم
            - يحتوي على هاشتاغات مناسبة
            - يدعو للتأمل والذكر
            - لا يزيد عن 200 كلمة
            
            استخدم هاشتاغات مثل: #قرآن #ذكر #دعاء #أمل #طمأنينة #إسلام
            """,
            
            'theme_suggestion': """
            اقترح موضوع إسلامي عميق ومؤثر من المواضيع التالية أو مشابه لها:
            - التوبة والمغفرة
            - الصبر والابتلاء
            - الرزق والتوكل
            - الرحمة والعفو
            - الأمل والرجاء
            - الشكر والحمد
            - الدعاء والذكر
            - الفرج بعد الضيق
            
            اذكر فقط اسم الموضوع بكلمة أو كلمتين.
            """
        }
    
    def generate_inspirational_phrase(self) -> Optional[str]:
        """توليد عبارة إلهامية جديدة"""
        try:
            # التأكد من وجود prompts
            if not hasattr(self, 'prompts'):
                self._initialize_prompts()

            response = self.model.generate_content(self.prompts['inspirational_phrase'])
            if response.text:
                # تنظيف النص وإزالة علامات الاقتباس إذا وجدت
                phrase = response.text.strip().strip('"').strip("'")
                return phrase
        except Exception as e:
            print(f"خطأ في توليد العبارة الإلهامية: {e}")
        
        return None
    
    def suggest_verse_by_theme(self, theme: str) -> Optional[str]:
        """اقتراح آية حسب الموضوع"""
        try:
            prompt = self.prompts['verse_suggestion'].format(theme=theme)
            response = self.model.generate_content(prompt)
            if response.text:
                return response.text.strip()
        except Exception as e:
            print(f"خطأ في اقتراح الآية: {e}")
        
        return None
    
    def generate_social_caption(self, verse: str, phrase: str) -> Optional[str]:
        """توليد تعليق لوسائل التواصل الاجتماعي"""
        try:
            prompt = self.prompts['caption_for_social'].format(
                verse=verse, 
                phrase=phrase
            )
            response = self.model.generate_content(prompt)
            if response.text:
                return response.text.strip()
        except Exception as e:
            print(f"خطأ في توليد التعليق: {e}")
        
        return None
    
    def suggest_random_theme(self) -> Optional[str]:
        """اقتراح موضوع عشوائي"""
        try:
            response = self.model.generate_content(self.prompts['theme_suggestion'])
            if response.text:
                return response.text.strip()
        except Exception as e:
            print(f"خطأ في اقتراح الموضوع: {e}")
        
        return None
    
    def enhance_content(self, verse_data: Dict, phrase: str) -> Dict:
        """تحسين المحتوى باستخدام Gemini"""
        enhanced_content = {
            'verse': verse_data,
            'phrase': phrase,
            'caption': None,
            'suggested_hashtags': []
        }
        
        # توليد تعليق محسن
        caption = self.generate_social_caption(verse_data['text'], phrase)
        if caption:
            enhanced_content['caption'] = caption
            
            # استخراج الهاشتاغات من التعليق
            hashtags = [word for word in caption.split() if word.startswith('#')]
            enhanced_content['suggested_hashtags'] = hashtags
        
        return enhanced_content
    
    def get_fallback_phrases(self) -> List[str]:
        """عبارات احتياطية في حالة فشل API"""
        return [
            "رَبِّي عَوِّضْنِي خَيْرًا عَنْ كُلِّ شَيْءٍ انْكَسَرَ بِنَفْسِي",
            "اللّهُمّ أرِنِي الفَرَحَ بِمُسْتَقْبَلِي",
            "اللّهُمّ عَوِّضْنِي بِالأَجْمَل يَا رَبّ",
            "يَا رَبّ اجْعَلْ لِي مِنْ كُلِّ هَمٍّ فَرَجًا",
            "اللّهُمّ اكْتُبْ لِي السَّعَادَةَ فِي قَلْبِي",
            "رَبِّي اشْرَحْ لِي صَدْرِي وَيَسِّرْ لِي أَمْرِي",
            "اللّهُمّ بَدِّلْ خَوْفِي أَمَانًا وَحُزْنِي فَرَحًا",
            "يَا رَبّ اجْعَلْ الخَيْرَ كُلَّهُ فِي مَا اخْتَرْتَهُ لِي"
        ]
    
    def _load_content_memory(self) -> Dict:
        """تحميل ذاكرة المحتوى المولد"""
        memory_file = f"data/content_memory_{self.user_id}.json"
        try:
            if os.path.exists(memory_file):
                with open(memory_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception:
            pass

        return {
            'generated_phrases': [],
            'used_themes': [],
            'recent_verses': [],
            'last_cleanup': datetime.now().isoformat()
        }

    def _save_content_memory(self):
        """حفظ ذاكرة المحتوى"""
        memory_file = f"data/content_memory_{self.user_id}.json"
        os.makedirs(os.path.dirname(memory_file), exist_ok=True)

        # تنظيف الذاكرة إذا كانت قديمة (أكثر من 30 يوم)
        last_cleanup = datetime.fromisoformat(self.content_memory.get('last_cleanup', datetime.now().isoformat()))
        if datetime.now() - last_cleanup > timedelta(days=30):
            self._cleanup_memory()

        with open(memory_file, 'w', encoding='utf-8') as f:
            json.dump(self.content_memory, f, ensure_ascii=False, indent=2)

    def _cleanup_memory(self):
        """تنظيف الذاكرة القديمة"""
        # الاحتفاظ بآخر 50 عبارة فقط
        if len(self.content_memory['generated_phrases']) > 50:
            self.content_memory['generated_phrases'] = self.content_memory['generated_phrases'][-50:]

        # الاحتفاظ بآخر 20 آية فقط
        if len(self.content_memory['recent_verses']) > 20:
            self.content_memory['recent_verses'] = self.content_memory['recent_verses'][-20:]

        self.content_memory['last_cleanup'] = datetime.now().isoformat()

    def _get_content_hash(self, content: str) -> str:
        """إنشاء hash للمحتوى لتجنب التكرار"""
        return hashlib.md5(content.encode('utf-8')).hexdigest()[:8]

    def generate_contextual_phrase(self, theme: str = "عام", time_context: str = "أي وقت",
                                 mood: str = "إيجابي", audience: str = "عام",
                                 creativity_level: str = "medium") -> Optional[str]:
        """توليد عبارة إلهامية حسب السياق"""
        try:
            # التأكد من تهيئة المكونات
            if not hasattr(self, 'creativity_levels'):
                self.creativity_levels = {
                    'low': {'temperature': 0.3, 'top_p': 0.8},
                    'medium': {'temperature': 0.7, 'top_p': 0.9},
                    'high': {'temperature': 0.9, 'top_p': 0.95}
                }
            if not hasattr(self, 'content_memory'):
                self.content_memory = {'generated_phrases': []}
            if not hasattr(self, 'advanced_prompts'):
                self.advanced_prompts = {
                    'contextual_phrase': """
أنشئ عبارة إلهامية قصيرة ومؤثرة باللغة العربية تناسب الموضوع: {theme}

المتطلبات:
- العبارة يجب أن تكون قصيرة (10-15 كلمة)
- مناسبة للوقت: {time_context}
- تناسب المزاج: {mood}
- مناسبة للجمهور: {audience}
- تجنب هذه العبارات السابقة: {previous_phrases}

أمثلة على العبارات الجيدة:
- اللهم اجعل القرآن ربيع قلوبنا
- ربي أرني الفرح بمستقبلي
- اللهم عوضني بالأجمل يا رب
- يا رب اجعل لي من كل هم فرجاً

اكتب العبارة فقط بدون أي إضافات:
                    """
                }

            # إعداد معاملات الإبداع
            generation_config = self.creativity_levels.get(creativity_level, self.creativity_levels['medium'])

            # الحصول على العبارات السابقة لتجنب التكرار
            previous_phrases = self.content_memory['generated_phrases'][-10:]  # آخر 10 عبارات

            prompt = self.advanced_prompts['contextual_phrase'].format(
                theme=theme,
                time_context=time_context,
                mood=mood,
                audience=audience,
                previous_phrases=', '.join(previous_phrases) if previous_phrases else "لا توجد"
            )

            response = self.model.generate_content(
                prompt,
                generation_config=genai.types.GenerationConfig(
                    temperature=generation_config['temperature'],
                    top_p=generation_config['top_p']
                )
            )

            if response.text:
                phrase = response.text.strip().strip('"').strip("'")

                # حفظ العبارة في الذاكرة
                phrase_hash = self._get_content_hash(phrase)
                if phrase_hash not in [self._get_content_hash(p) for p in previous_phrases]:
                    self.content_memory['generated_phrases'].append(phrase)
                    self._save_content_memory()

                return phrase

        except Exception as e:
            print(f"خطأ في توليد العبارة السياقية: {e}")

        return None

    def suggest_smart_verse(self, main_theme: str, sub_themes: List[str] = None,
                          purpose: str = "تذكير", complexity: str = "متوسط") -> Optional[Dict]:
        """اقتراح آية ذكي حسب المعايير"""
        try:
            sub_themes = sub_themes or []
            recent_verses = self.content_memory['recent_verses'][-5:]  # آخر 5 آيات

            prompt = self.advanced_prompts['smart_verse_selection'].format(
                main_theme=main_theme,
                sub_themes=', '.join(sub_themes),
                purpose=purpose,
                complexity=complexity,
                recent_verses=', '.join(recent_verses) if recent_verses else "لا توجد"
            )

            response = self.model.generate_content(prompt)

            if response.text:
                # تحليل الاستجابة لاستخراج معلومات الآية
                suggestion = self._parse_verse_suggestion(response.text)

                if suggestion:
                    # حفظ الآية في الذاكرة
                    verse_id = f"{suggestion.get('surah', '')}:{suggestion.get('verse_number', '')}"
                    if verse_id not in recent_verses:
                        self.content_memory['recent_verses'].append(verse_id)
                        self._save_content_memory()

                return suggestion

        except Exception as e:
            print(f"خطأ في اقتراح الآية الذكي: {e}")

        return None

    def _parse_verse_suggestion(self, response_text: str) -> Optional[Dict]:
        """تحليل استجابة اقتراح الآية"""
        try:
            lines = response_text.strip().split('\n')
            suggestion = {}

            for line in lines:
                line = line.strip()
                if line.startswith('نص الآية:') or line.startswith('الآية:'):
                    suggestion['text'] = line.split(':', 1)[1].strip()
                elif line.startswith('السورة:') or line.startswith('اسم السورة:'):
                    suggestion['surah'] = line.split(':', 1)[1].strip()
                elif line.startswith('رقم الآية:') or line.startswith('الآية رقم:'):
                    suggestion['verse_number'] = line.split(':', 1)[1].strip()
                elif line.startswith('السبب:') or line.startswith('سبب الاختيار:'):
                    suggestion['reason'] = line.split(':', 1)[1].strip()

            return suggestion if suggestion.get('text') else None

        except Exception:
            return None

    def generate_dynamic_caption(self, verse_text: str, surah_name: str, verse_number: str,
                               inspirational_phrase: str, post_style: str = "ملهم",
                               platform: str = "عام", caption_length: str = "متوسط") -> Optional[str]:
        """توليد تعليق ديناميكي ومتنوع"""
        try:
            prompt = self.advanced_prompts['dynamic_caption'].format(
                verse_text=verse_text,
                surah_name=surah_name,
                verse_number=verse_number,
                inspirational_phrase=inspirational_phrase,
                post_style=post_style,
                platform=platform,
                caption_length=caption_length
            )

            response = self.model.generate_content(prompt)

            if response.text:
                return response.text.strip()

        except Exception as e:
            print(f"خطأ في توليد التعليق الديناميكي: {e}")

        return None

    def get_enhanced_phrase(self, context: Dict = None) -> str:
        """الحصول على عبارة محسنة (مولدة أو احتياطية)"""
        try:
            if context:
                # استخدام التوليد السياقي
                generated_phrase = self.generate_contextual_phrase(
                    theme=context.get('theme', 'عام'),
                    time_context=context.get('time_context', 'أي وقت'),
                    mood=context.get('mood', 'إيجابي'),
                    audience=context.get('audience', 'عام'),
                    creativity_level=context.get('creativity_level', 'medium')
                )
            else:
                # استخدام التوليد العادي
                generated_phrase = self.generate_inspirational_phrase()

            if generated_phrase:
                return generated_phrase

            # في حالة الفشل، استخدام عبارة احتياطية
            fallback_phrases = self.get_fallback_phrases()
            return random.choice(fallback_phrases)

        except Exception as e:
            print(f"خطأ في توليد العبارة الإلهامية: {e}")
            return "اللهم بارك لنا"
